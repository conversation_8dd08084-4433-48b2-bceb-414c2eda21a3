import os
from dotenv import load_dotenv
from ell import Model, Agent, Planner, AgentList
from openai import OpenAI

# Load environment
load_dotenv()
OLLAMA_URL = os.getenv("OLLAMA_URL")

# ---- MODELLER ----

# Planner använder <PERSON> (ex. mistral)
planner_model = Model(
    id="ollama-planner",
    base_url=OLLAMA_URL,
    model="llama4",   # eller annan modell du laddat
    provider="openai"
)

# MCP-agenter (math & text)
math_model = Model(
    id="math",
    base_url="http://localhost:8001/v1",
    model="math",
    provider="openai"
)

text_model = Model(
    id="text",
    base_url="http://localhost:8002/v1",
    model="text",
    provider="openai"
)

# ---- AGENTER ----

math_agent = Agent(id="math-agent", model=math_model)
text_agent = Agent(id="text-agent", model=text_model)

# ---- PLANNER ----

planner = Planner(
    id="router",
    model=planner_model,
    agents=AgentList([math_agent, text_agent]),
    system_prompt="""
Du är en agent-router. Du får en användarfråga och ska välja den bästa agenten för att svara.
- Använd 'math-agent' om frågan handlar om matematik, t.ex. tal, räkning, multiplikation, addition.
- Använd 'text-agent' om frågan handlar om ord, meningar, antal bokstäver eller att vända på text.

Svara endast med slutresultatet från rätt agent.
"""
)

# ---- TEST ----

questions = [
    "Vad blir 4 * 9?",
    "Hur många bokstäver är det i 'hallå världen'?",
    "Skriv ordet 'agent' baklänges",
    "Vad blir 5 + 3 + 2?"
]

for q in questions:
    response = planner(q)
    print(f"🧠 {q}\n➡️  {response.content}\n")
