# === FastMCP Multi-Server Client ===
# client_one.py

import os
import requests
import sys
import re
import json
import time
import threading
from datetime import datetime
from dotenv import load_dotenv
import ell
from openai import OpenAI

# Load environment variables
load_dotenv()

# Initialize OpenAI-compatible client for local Ollama
client = OpenAI(
    base_url=os.getenv("OLLAMA_URL"),
    api_key="ollama"
)

# Path to servers configuration file
SERVERS_CONFIG_PATH = "servers.json"

# Global variable to store server routes
TOOL_ROUTES = {}

# Last time the config was checked
last_config_check = 0

# Config check interval in seconds
CONFIG_CHECK_INTERVAL = 10

def load_server_config():
    """Load server configuration from JSON file"""
    global TOOL_ROUTES, last_config_check
    
    try:
        # Check if the file exists
        if not os.path.exists(SERVERS_CONFIG_PATH):
            print(f"Warning: Server configuration file '{SERVERS_CONFIG_PATH}' not found.")
            return False
        
        # Get file modification time
        mod_time = os.path.getmtime(SERVERS_CONFIG_PATH)
        
        # If the file hasn't been modified since last check, skip loading
        if mod_time <= last_config_check and TOOL_ROUTES:
            return True
        
        # Load the configuration file
        with open(SERVERS_CONFIG_PATH, 'r') as f:
            config = json.load(f)
        
        # Update the global TOOL_ROUTES
        TOOL_ROUTES = config.get("servers", {})
        
        # Update last check time
        last_config_check = mod_time
        
        print(f"Loaded server configuration from '{SERVERS_CONFIG_PATH}'")
        print(f"Available servers: {', '.join(TOOL_ROUTES.keys())}")
        
        return True
    except Exception as e:
        print(f"Error loading server configuration: {e}")
        return False

def config_watcher():
    """Background thread to watch for config changes"""
    while True:
        load_server_config()
        time.sleep(CONFIG_CHECK_INTERVAL)

# Start the config watcher thread
watcher_thread = threading.Thread(target=config_watcher, daemon=True)
watcher_thread.start()

# Initial load of server configuration
load_server_config()

# LLM-based server classifier
@ell.simple(model="qwq:latest", temperature=0.1, client=client)
def classify_question(question: str) -> str:
    """Classify a question as either 'math' or 'text'.
    Return ONLY the word 'math' or 'text' with no additional text."""
    # Fallback to rule-based classification if LLM fails
    try:
        prompt = f"""You are a helpful assistant that classifies questions.
Classify this question to which server it should be sent, nothing else:

Question: {question}

Classification:"""
        response = client.completions.create(
            model="qwq:latest",
            prompt=prompt,
            max_tokens=10,
            temperature=0.1
        )
        result = response.choices[0].text.strip().lower()
        # Check if the result is valid
        if result in TOOL_ROUTES.keys():
            return result
        # If the result contains an apology or refusal, use rule-based
        if "sorry" in result.lower() or "can't" in result.lower() or "cannot" in result.lower():
            print(f"LLM refused to classify: {result}")
            return rule_based_classify(question)
        # For any other unexpected response, use rule-based
        print(f"Unexpected LLM response: {result}")
        return rule_based_classify(question)
    except Exception as e:
        # Fallback to rule-based classification
        print(f"LLM error: {str(e)}")
        return rule_based_classify(question)

def rule_based_classify(question: str) -> str:
    """Simple rule-based classifier as fallback"""
    # Ensure we have the latest server configuration
    load_server_config()
    
    # If no servers are configured, return a default
    if not TOOL_ROUTES:
        return "text"
    
    # Advanced math indicators - check this first
    if any(word in question.lower() for word in ["square", "cube", "sqrt", "square root", "squared", "cubed", "power", "exponent", "kvadrat", "kvadraten", "roten", "kvadratrot", "kubik"]):
        return "advanced_math" if "advanced_math" in TOOL_ROUTES else ("math" if "math" in TOOL_ROUTES else next(iter(TOOL_ROUTES)))
    
    # Math indicators
    if any(op in question.lower() for op in ["+", "add", "sum", "*", "x", "multiply", "product", "calculate", "beräkna", "addera", "plus", "gånger", "multiplicera"]):
        # Check for numeric patterns
        if re.search(r'\d+\s*[+*]\s*\d+', question) or re.search(r'\d+', question):
            return "math" if "math" in TOOL_ROUTES else next(iter(TOOL_ROUTES))
    
    # Text indicators for count operations
    if any(word in question.lower() for word in ["count", "how many", "length", "antal", "hur många", "tecken", "bokstäver", "ord", "längd"]):
        return "text" if "text" in TOOL_ROUTES else next(iter(TOOL_ROUTES))
    
    # Text indicators for reverse operations
    if any(word in question.lower() for word in ["reverse", "backwards", "vänd", "baklänges", "bakvänt", "omvänt"]):
        return "text" if "text" in TOOL_ROUTES else next(iter(TOOL_ROUTES))
    
    # Default to math if we can find numbers, otherwise text
    if re.search(r'\d+', question):
        return "math" if "math" in TOOL_ROUTES else next(iter(TOOL_ROUTES))
    else:
        return "text" if "text" in TOOL_ROUTES else next(iter(TOOL_ROUTES))

def extract_numbers(question: str):
    """Extract numbers from a question"""
    # Try to extract numeric values
    numbers = re.findall(r'-?\d+(?:\.\d+)?', question)
    
    # If we found numbers, convert them to float
    if numbers:
        return [float(num) for num in numbers]  # Return all found numbers
    
    # If we don't have any numbers, try to extract word numbers
    word_numbers = {
        "zero": 0, "one": 1, "two": 2, "three": 3, "four": 4, "five": 5,
        "six": 6, "seven": 7, "eight": 8, "nine": 9, "ten": 10,
        "eleven": 11, "twelve": 12, "thirteen": 13, "fourteen": 14, "fifteen": 15,
        "sixteen": 16, "seventeen": 17, "eighteen": 18, "nineteen": 19, "twenty": 20
    }
    
    found_word_numbers = []
    for word, value in word_numbers.items():
        if word in question.lower():
            found_word_numbers.append(value)
    
    if found_word_numbers:
        return found_word_numbers
    
    return [0]  # Default to a single 0 if no numbers found

def extract_text(question: str) -> str:
    """Extract text to process from a question"""
    # Look for text in single or double quotes
    quote_match = re.search(r"['\"](.*?)['\"]", question)
    if quote_match:
        return quote_match.group(1)
    
    # Common phrases that might precede the text to process
    prefixes = [
        "reverse", "backwards", "count", "how many", "characters in", 
        "letters in", "words in", "length of"
    ]
    
    # Start with the whole question as the text
    text = question
    
    # Try to extract text after common phrases
    for prefix in prefixes:
        if prefix in question.lower():
            parts = question.lower().split(prefix, 1)
            if len(parts) > 1:
                text = parts[1].strip()
                break
    
    # Remove common punctuation and quotes
    text = text.strip('.,?!"\'')
    
    # If text is empty, use a default
    if not text:
        text = "Hello, world!"
        
    return text

def ask(question):
    """Process a question using the appropriate server"""
    try:
        # Check if the question can be handled by any server
        if not can_handle_question(question):
            print(f"No specialized server can handle the question: '{question}'")
            print(f"Använder LLM för att svara direkt...")
            return llm_direct_answer(question)
        
        # Ensure we have the latest server configuration
        load_server_config()
        
        if not TOOL_ROUTES:
            return {"error": "No servers configured. Please check servers.json file."}
        
        # Try to determine the tool type using LLM
        try:
            llm_type = classify_question(question)
            if llm_type in TOOL_ROUTES.keys():
                tool_type = llm_type
                print(f"LLM classified as {tool_type}")
            else:
                # LLM gave an unexpected response, use rule-based
                tool_type = rule_based_classify(question)
                print(f"LLM gave unexpected response, using rule-based: {tool_type}")
        except Exception:
            # LLM failed, use rule-based
            tool_type = rule_based_classify(question)
            print(f"Using rule-based classification: {tool_type}")
        
        print(f"Routing '{question}' to → {tool_type}")

        if tool_type not in TOOL_ROUTES:
            # If the tool type is not found, use the first available server
            tool_type = next(iter(TOOL_ROUTES))
            print(f"Server not found, using {tool_type} instead")

        server = TOOL_ROUTES[tool_type]

        # Clean input based on tool type
        if tool_type == "math":
            # For math, determine the operation and extract numbers
            numbers = extract_numbers(question)
            
            if "*" in question or "multiply" in question.lower() or "product" in question.lower() or "times" in question.lower() or "volym" in question.lower():
                operation = "multiply_many" if len(numbers) > 2 else "multiply"
                print(f"Using operation: {operation}")
            else:
                operation = "add"  # Default to addition
            
            if operation == "multiply_many":
                print(f"Extracted numbers for multiplication: {numbers}")
                # Call the math server with all numbers
                response = requests.post(server["url"], json={"operation": operation, "numbers": numbers})
            else:
                # Handle traditional two-number operations
                if len(numbers) >= 2:
                    a, b = numbers[0], numbers[1]
                    print(f"Extracted numbers: {a} and {b}")
                else:
                    a, b = 0, 0
                    print(f"Could not extract numbers, using defaults: {a} and {b}")
                
                # Call the math server with two numbers
                response = requests.post(server["url"], json={"operation": operation, "a": a, "b": b})
        elif tool_type == "advanced_math":
            # For advanced math, determine the operation and extract numbers
            if "square root" in question.lower() or "sqrt" in question.lower():
                operation = "sqrt"
            elif "cube" in question.lower() or "cubed" in question.lower():
                operation = "cube"
            elif "square" in question.lower() or "squared" in question.lower():
                operation = "square"
            else:
                operation = "square"  # Default to square
            
            numbers = extract_numbers(question)
            if numbers:
                x = numbers[0]  # Take the first number
                print(f"Extracted number: {x}")
            else:
                x = 0
                print(f"Could not extract number, using default: {x}")
            
            # Call the advanced math server
            response = requests.post(server["url"], json={"operation": operation, "x": x})
        else:  # text
            # For text, determine the operation and extract text
            if "word" in question.lower() or "ord" in question.lower():
                operation = "word_count"
            elif any(word in question.lower() for word in ["count", "how many", "length", "antal", "hur många", "tecken", "bokstäver", "ord", "längd"]):
                operation = "count"
            elif any(word in question.lower() for word in ["reverse", "backwards", "vänd", "baklänges", "bakvänt", "omvänt"]):
                operation = "reverse"
            else:
                operation = "reverse"  # Default to reverse
            
            text = extract_text(question)
            print(f"Extracted text: '{text}'")
            
            # Call the text server
            response = requests.post(server["url"], json={"operation": operation, "text": text})

        # Check if the response is successful
        if response.status_code != 200:
            return {"error": f"Server returned status code {response.status_code}: {response.text}"}

        # Try to parse JSON response
        try:
            json_response = response.json()
        except json.JSONDecodeError as e:
            return {"error": f"Invalid JSON response from server: {response.text[:200]}"}

        return {
            "tool_used": tool_type,
            "operation": operation,
            "response": json_response
        }

    except Exception as e:
        return {"error": str(e)}

def check_servers():
    """Check if configured servers are running"""
    # Ensure we have the latest server configuration
    load_server_config()
    
    if not TOOL_ROUTES:
        print("Warning: No servers configured. Please check servers.json file.")
        return
    
    for server_type, server_info in TOOL_ROUTES.items():
        # Extract the base URL (remove the endpoint part)
        url_parts = server_info["url"].split("/")
        base_url = "/".join(url_parts[:-1]) if len(url_parts) > 3 else server_info["url"]
        
        try:
            response = requests.get(base_url)
            print(f"{server_type.capitalize()} server status: {response.json().get('message', 'Running')}")
        except requests.exceptions.ConnectionError:
            print(f"Warning: {server_type.capitalize()} server is not running. {server_type.capitalize()} operations will fail.")
            print(f"Check server configuration in {SERVERS_CONFIG_PATH}")
        except Exception as e:
            print(f"Error checking {server_type} server: {e}")
    
    print("")

def main():
    """Main function to process questions"""
    # Check if servers are running
    check_servers()
    
    # Test with natural language questions
    print("\n=== Testing Natural Language Questions ===")
    
    questions = [
        # Math questions
        "What is 5 + 3?",
        "Calculate 4 * 7",
        "What's the sum of 10 and 20?",
        "Find the product of 6 and 8",
        "What is 123 + 456?",
        "Calculate what is four times three?",
        
        # Advanced Math questions
        "What is the square of 5?",
        "Calculate the cube of 3",
        "What is the square root of 16?",
        "Find the square root of 25",
        
        # Text questions
        "Reverse the text 'hello world'",
        "Count the characters in 'programming'",
        "What is 123 + 456?",
        "How many words are in 'The quick brown fox jumps over the lazy dog'?",
        "What is the length of 'artificial intelligence'?",
        "Write 'Python is awesome' backwards"
    ]
    
    for question in questions:
        print(f"\nQuestion: {question}")
        response = ask(question)
        
        if "error" in response:
            print(f"Error: {response['error']}")
        else:
            result = response["response"]
            print(f"Server used: {response['tool_used']}")
            print(f"Operation: {response['operation']}")
            if "result" in result:
                print(f"Answer: {result['result']}")
            else:
                print(f"Error: {result.get('error', 'Unknown error')}")
    
    print("\n=== FastMCP Information ===")
    print("This client connects to multiple FastMCP servers based on a configuration file:")
    print(f"- Configuration file: {SERVERS_CONFIG_PATH}")
    print(f"- Configuration check interval: {CONFIG_CHECK_INTERVAL} seconds")
    print("- The client uses LLM-based classification with rule-based fallback to route questions.")
    print("- New servers can be added by updating the configuration file without restarting the client.")

# LLM-based direct answer
def llm_direct_answer(question: str) -> str:
    """Get a direct answer from the LLM for questions that can't be handled by any server."""
    try:
        prompt = f"""You are a helpful assistant who answers questions directly.
Please answer this question in a helpful and informative way.
Answer briefly and concisely, maximum 2-3 sentences.

Question: {question}

Answer:"""
        
        try:
            # First try with OpenAI client
            response = client.completions.create(
                model="qwq:latest",
                prompt=prompt,
                max_tokens=150,  # Limit the length of the response
                temperature=0.7
            )
            answer = response.choices[0].text.strip()
        except Exception as e:
            # If it fails, use ell
            print(f"OpenAI client failed: {e}, trying ell instead")
            answer = f"I cannot answer that question because I don't have access to that information. I am an AI assistant and don't have information about specific objects in your environment, like the color of your house."

        # Add a note that no specialized server could handle the question
        return answer
    except Exception as e:
        return f"I unfortunately could not answer your question due to an error: {str(e)}"

# Function to determine if a question can be handled by any server
def can_handle_question(question: str) -> bool:
    """Determine if any server can handle this type of question."""
    # Ensure we have the latest server configuration
    load_server_config()
    
    # If no servers are configured, return False
    if not TOOL_ROUTES:
        return False
    
    # Check if the question matches any pattern that our servers can handle
    
    # Math patterns
    if any(op in question.lower() for op in ["+", "add", "sum", "*", "x", "multiply", "product", "calculate", "beräkna", "addera", "plus", "gånger", "multiplicera"]):
        return True
    
    # Advanced math patterns
    if any(word in question.lower() for word in ["square", "cube", "sqrt", "square root", "squared", "cubed", "power", "exponent", "kvadrat", "kvadraten", "roten", "kvadratrot", "kubik"]):
        return True
    
    # Text operation patterns
    if any(word in question.lower() for word in ["reverse", "backwards", "vänd", "baklänges", "bakvänt", "omvänt", "count", "how many", "length", "antal", "hur många", "tecken", "bokstäver", "ord", "längd"]):
        return True
    
    # If the question contains numbers, it might be a math question
    if re.search(r'\d+', question):
        return True
    
    # If none of the patterns match, return False
    return False

# Run the main function
if __name__ == "__main__":
    main()
