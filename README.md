# Multi-Component Processing (MCP) System

The Multi-Component Processing (MCP) system is a modular architecture that routes questions to specialized servers based on their content. It uses a client router with LLM-based classification to determine which specialized server should handle each request.

## System Architecture

The system consists of the following components:

### 1. Host Interface (`host.py`)

Provides a user-friendly interface for interacting with the MCP system. It includes:
- Interactive mode for asking questions
- Test mode for running predefined test questions
- Server information display
- Debug mode toggle
- **Imports and uses the client router (`client_one.py`)**

### 2. Client Router (`client_one.py`)

Responsible for routing questions to the appropriate specialized server:
- **Used by host.py to process and route questions**
- Uses LLM-based classification to determine question type
- Includes a rule-based fallback mechanism
- Extracts relevant data from questions (numbers, text)
- Monitors server configuration changes

### 3. Specialized Servers

#### Math Server (`server_one.py`)
- Handles basic mathematical operations
- Supports addition and multiplication
- Supports multi-number multiplication (for volume calculations)
- Runs on port 8004

#### Text Server (`server_two.py`)
- Processes text operations
- Supports text reversal, character counting, and word counting
- Runs on port 8005

#### Advanced Math Server (`server_three.py`)
- Handles more complex mathematical operations
- Supports square, cube, and square root calculations
- Runs on port 8006

### 4. Configuration (`servers.json`)

Stores server configuration including:
- Server URLs
- Available operations
- Server descriptions

### 5. System Launcher (`run.py`)

Provides a convenient way to start all components with a single command:
- Starts all specialized servers in separate windows
- Launches the host interface
- Automatically cleans up processes on exit
- Handles Ctrl+C and other termination signals

## Flow Diagram

```mermaid
flowchart TD
    User[User] --> Host[Host Interface\nhost.py]
    Host -->|Imports and uses| Client[Client Router\nclient_one.py]
    Client --> Classifier{LLM Classifier}
    Classifier -->|Math Question| MathServer[Math Server\nserver_one.py]
    Classifier -->|Text Question| TextServer[Text Server\nserver_two.py]
    Classifier -->|Advanced Math| AdvMathServer[Advanced Math Server\nserver_three.py]
    Classifier -->|Unknown| Fallback[Rule-based Fallback]
    Fallback --> MathServer
    Fallback --> TextServer
    Fallback --> AdvMathServer
    MathServer --> Response[Response]
    TextServer --> Response
    AdvMathServer --> Response
    Response --> Host
    Host --> User
    
    Config[Configuration\nservers.json] -.-> Client
    Launcher[System Launcher\nrun.py] -.->|Starts| MathServer
    Launcher -.->|Starts| TextServer
    Launcher -.->|Starts| AdvMathServer
    Launcher -.->|Starts| Host
```

## Key Features

1. **LLM-based Classification**: Uses a local LLM to classify questions and route them to specialized servers.
2. **Rule-based Fallback**: If the LLM fails or gives unexpected answers, falls back to rule-based classification.
3. **Dynamic Configuration**: Monitors the server configuration file for changes and updates routing accordingly.
4. **Specialized Processing**: Each server is optimized for specific types of operations.
5. **Interactive Interface**: User-friendly interface for asking questions and viewing responses.
6. **Multi-Number Operations**: Support for operations with multiple numbers, such as volume calculations.
7. **One-Click Startup**: Launch the entire system with a single command using run.py.

## Math Server Updates

The math server has been updated to handle any addition operation, not just specific cases like "5 + 3". It now uses regex to extract numbers from expressions and sums them together. Additionally, it can now handle multiplication of multiple numbers (more than two), which is especially useful for volume calculations.

## Client Router Improvements

The client router uses an LLM-based classifier with a precise prompt that gives clean answers (only "math" or "text"), and includes a rule-based fallback mechanism in case the LLM fails or gives unexpected answers. The system now supports sending any numbers for addition and multiplication through the host.py interface and correctly routes math questions to the math server.

## Getting Started

### Option 1: Using the System Launcher (Recommended)

Start the entire system with a single command:
```
python run.py
```
This will launch all servers and the host interface in appropriate windows.

### Option 2: Manual Startup

1. Start the specialized servers:
   ```
   python server_one.py
   python server_two.py
   python server_three.py
   ```

2. Run the host interface:
   ```
   python host.py
   ```
   **Note: The host.py file automatically imports and uses client_one.py as the client router, so you don't need to start client_one.py separately.**

3. Ask questions in the interactive mode or view the results of the test questions.

## Example Questions

- "What is 5 + 3?" (Math Server)
- "Vänd texten 'Hej världen'!" (Text Server)
- "Vad är kvadraten av 7?" (Advanced Math Server)
- "Hur många tecken finns i 'programmering'?" (Text Server)
- "Jag har en låda som är 5*5*6 cm, vad är volymen?" (Math Server, using multi-number multiplication)
