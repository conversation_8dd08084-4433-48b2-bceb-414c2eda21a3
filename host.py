# === HOST ===
# host.py
import sys
import os
import json
import time

# Add path to import client_one
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import ask function from client_one
from client_one import ask, load_server_config

# Disable prints from client_one
def disable_client_prints():
    """Disable prints from client_one"""
    import builtins
    original_print = builtins.print

    def custom_print(*args, **kwargs):
        # Filter out certain prints from client_one
        if len(args) > 0:
            text = str(args[0])
            if any(s in text for s in ["Loaded server configuration", "Available servers", "Routing", "Extracted", "LLM", "Unexpected"]):
                return
        original_print(*args, **kwargs)

    builtins.print = custom_print

# Enable prints from client_one for debugging
debug_mode = False
if not debug_mode:
    disable_client_prints()

def ask_question(question):
    """Send a question to client_one and get an answer"""
    # Ensure server information is loaded
    load_server_config()

    # Call ask function from client_one
    response = ask(question)

    # Format the response for output
    if isinstance(response, str):
        # This is a direct answer from LLM
        return {
            "server": "llm_direct",
            "operation": "direct_answer",
            "result": response,
            "note": "No specialized server could handle this question, so I answered directly."
        }
    elif "error" in response:
        return {"error": response["error"]}

    result = response["response"]
    return {
        "server": response["tool_used"],
        "operation": response["operation"],
        "result": result.get("result", "No result"),
        "details": result
    }

def print_server_info():
    """Show information about available servers"""
    try:
        with open("servers.json", "r") as f:
            config = json.load(f)

        servers = config.get("servers", {})
        print(f"\nAvailable servers ({len(servers)}):\n")

        for name, info in servers.items():
            print(f"- {name.upper()}:")
            print(f"  URL: {info['url']}")
            print(f"  Operations: {', '.join(info['operations'])}")
            print(f"  Description: {info.get('description', 'No description')}\n")
    except Exception as e:
        print(f"Could not read server information: {e}")

def interactive_mode():
    """Interactive mode where users can ask questions"""
    print("\n=== MCP Interactive Mode ===")
    print("Type 'exit', 'quit' or 'q' to exit")
    print("Type 'servers' to show information about available servers")
    print("Type 'debug on' or 'debug off' to enable/disable debug mode")

    while True:
        try:
            question = input("\nQuestion: ")

            if question.lower() in ["exit", "quit", "q"]:
                print("Exiting...")
                break

            if question.lower() == "servers":
                print_server_info()
                continue

            if question.lower() == "debug on":
                global debug_mode
                debug_mode = True
                import builtins
                builtins.print = builtins.__original_print__ if hasattr(builtins, "__original_print__") else print
                print("Debug mode enabled")
                continue

            if question.lower() == "debug off":
                debug_mode = False
                disable_client_prints()
                print("Debug mode disabled")
                continue

            if not question.strip():
                continue

            print("Processing question...")
            response = ask_question(question)

            print("\nAnswer:")
            if "error" in response:
                print(f"Error: {response['error']}")
            else:
                print(f"Server: {response['server']}")
                print(f"Operation: {response['operation']}")
                print(f"Result: {response['result']}")
                if "note" in response:
                    print(f"Note: {response['note']}")
        except KeyboardInterrupt:
            print("\nExiting...")
            break
        except Exception as e:
            print(f"An error occurred: {e}")

def run_test_questions():
    """Run some test questions"""
    print("=== Test Questions ===")

    test_questions = [
        "What is 5 + 3?",
        "Reverse the text 'Hello world'!",
        "What is the square of 7?",
        "How many characters are in 'programming'?"
    ]

    for question in test_questions:
        print(f"\nQuestion: {question}")
        response = ask_question(question)
        if "error" in response:
            print(f"Error: {response['error']}")
        else:
            print(f"Server: {response['server']}")
            print(f"Operation: {response['operation']}")
            print(f"Result: {response['result']}")
        # Add a short pause between questions
        time.sleep(0.5)

if __name__ == "__main__":
    # Save original print function
    import builtins
    builtins.__original_print__ = builtins.print

    # Show information about available servers
    print_server_info()

    # Run test questions
    run_test_questions()

    # Start interactive mode
    interactive_mode()