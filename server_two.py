# === FastMCP Text Server ===
# server_two.py

from fastmcp import FastMCP
import uvicorn
from fastapi import <PERSON><PERSON><PERSON>, Request, Response
from fastapi.middleware.cors import CORSMiddleware
import json

# Create a FastMCP server
mcp = FastMCP("TextProcessor")

# Add a reverse text tool
@mcp.tool()
def reverse(text: str) -> str:
    """Reverse the input text"""
    print(f"REVERSE received: {text}")
    return text[::-1]

# Add a character count tool
@mcp.tool()
def count(text: str) -> int:
    """Count the number of characters in the text"""
    print(f"COUNT received: {text}")
    return len(text)

# Add a word count tool
@mcp.tool()
def word_count(text: str) -> int:
    """Count the number of words in the text"""
    print(f"WORD COUNT received: {text}")
    return len(text.split())

# Add a text resource
@mcp.resource("text://{name}")
def get_sample_text(name: str) -> str:
    """Get a sample text by name"""
    samples = {
        "greeting": "Hello, world!",
        "quote": "To be or not to be, that is the question.",
        "lorem": "Lorem ipsum dolor sit amet, consectetur adipiscing elit."
    }
    return samples.get(name, f"No sample text found for '{name}'")

# Add a simple prompt template
@mcp.prompt("text_help")
def text_help() -> str:
    """Return help text for the text processor"""
    return """
    This is a text processor that can perform various operations on text.
    
    Available tools:
    - reverse(text): Reverse the input text
    - count(text): Count the number of characters in the text
    - word_count(text): Count the number of words in the text
    
    Example usage:
    - reverse("hello") = "olleh"
    - count("hello") = 5
    - word_count("hello world") = 2
    """

# Create a FastAPI app
app = FastAPI()

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Add a simple endpoint to check if the server is running
@app.get("/")
async def root():
    return {"message": "FastMCP Text Processor Server is running"}

# Add an endpoint to handle requests in the format similar to your existing servers
@app.post("/ask")
async def ask(request: Request):
    data = await request.json()
    operation = data.get("operation", "reverse")
    text = data.get("text", "")
    
    if operation == "reverse":
        result = reverse(text)
        return {"result": result, "operation": "reverse"}
    elif operation == "count":
        result = count(text)
        return {"result": result, "operation": "count"}
    elif operation == "word_count":
        result = word_count(text)
        return {"result": result, "operation": "word_count"}
    else:
        return {"error": f"Unknown operation: {operation}"}

# Run the server
if __name__ == "__main__":
    print("Starting FastMCP Text Processor Server on http://127.0.0.1:8005")
    uvicorn.run(app, host="127.0.0.1", port=8005)
