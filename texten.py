from fastmcp import FastMCP

mcp = FastMCP("text")

@mcp.tool()
def reverse(text: str) -> str:
    """Reverse the input text"""
    print(f"REVERSE received: {text}")
    return text[::-1]

# Add a character count tool
@mcp.tool()
def count(text: str) -> int:
    """Count the number of characters in the text"""
    print(f"COUNT received: {text}")
    return len(text)

# Add a word count tool
@mcp.tool()
def word_count(text: str) -> int:
    """Count the number of words in the text"""
    print(f"WORD COUNT received: {text}")
    return len(text.split())

if __name__ == "__main__":
    mcp.run(transport="http", port=8002)