from fastmcp import FastMCP

mcp = FastMCP("math")

# Add an addition tool
@mcp.tool()
def add(a: int, b: int) -> int:
    """Add two numbers"""
    print(f"ADDITION received: {a} + {b}")
    return a + b

# Add a multiplication tool
@mcp.tool()
def multiply(a: int, b: int) -> int:
    """Multiply two numbers"""
    print(f"MULTIPLICATION received: {a} * {b}")
    return a * b

# Add a multi-multiplication tool for handling multiple numbers
@mcp.tool()
def multiply_many(numbers: list) -> int:
    """Multiply multiple numbers"""
    print(f"MULTI-MULTIPLICATION received: {numbers}")
    if not numbers:
        return 0
    result = 1
    for num in numbers:
        result *= num
    return result

if __name__ == "__main__":
    mcp.run(transport="hstiottp", port=8001)