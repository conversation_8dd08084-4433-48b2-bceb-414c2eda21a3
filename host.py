# === HOST ===
# host.py
import sys
import os
import json
import time

# <PERSON>ägg till sökvägen för att importera client_one
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Importera ask-funktionen från client_one
from client_one import ask, load_server_config

# Inaktivera utskrifter från client_one
def disable_client_prints():
    """Inaktivera utskrifter från client_one"""
    import builtins
    original_print = builtins.print
    
    def custom_print(*args, **kwargs):
        # Filtrera bort vissa utskrifter från client_one
        if len(args) > 0:
            text = str(args[0])
            if any(s in text for s in ["Loaded server configuration", "Available servers", "Routing", "Extracted", "LLM", "Unexpected"]):
                return
        original_print(*args, **kwargs)
    
    builtins.print = custom_print

# Aktivera utskrifter från client_one för debugging
debug_mode = False
if not debug_mode:
    disable_client_prints()

def ask_question(question):
    """Skicka en fråga till client_one och få ett svar"""
    # Se till att serverinformationen är inläst
    load_server_config()
    
    # Anropa ask-funktionen från client_one
    response = ask(question)
    
    # Formatera svaret för utskrift
    if isinstance(response, str):
        # Detta är ett direkt svar från LLM
        return {
            "server": "llm_direct",
            "operation": "direct_answer",
            "result": response,
            "note": "Ingen specialiserad server kunde hantera denna fråga, så jag svarade direkt."
        }
    elif "error" in response:
        return {"error": response["error"]}
    
    result = response["response"]
    return {
        "server": response["tool_used"],
        "operation": response["operation"],
        "result": result.get("result", "Inget resultat"),
        "details": result
    }

def print_server_info():
    """Visa information om tillgängliga servrar"""
    try:
        with open("servers.json", "r") as f:
            config = json.load(f)
        
        servers = config.get("servers", {})
        print(f"\nTillgängliga servrar ({len(servers)}):\n")
        
        for name, info in servers.items():
            print(f"- {name.upper()}:")
            print(f"  URL: {info['url']}")
            print(f"  Operationer: {', '.join(info['operations'])}")
            print(f"  Beskrivning: {info.get('description', 'Ingen beskrivning')}\n")
    except Exception as e:
        print(f"Kunde inte läsa serverinformation: {e}")

def interactive_mode():
    """Interaktivt läge där användaren kan ställa frågor"""
    print("\n=== MCP Interaktivt Läge ===")
    print("Skriv 'exit', 'quit' eller 'q' för att avsluta")
    print("Skriv 'servers' för att visa information om tillgängliga servrar")
    print("Skriv 'debug on' eller 'debug off' för att aktivera/inaktivera debug-läge")
    
    while True:
        try:
            question = input("\nFråga: ")
            
            if question.lower() in ["exit", "quit", "q"]:
                print("Avslutar...")
                break
            
            if question.lower() == "servers":
                print_server_info()
                continue
            
            if question.lower() == "debug on":
                global debug_mode
                debug_mode = True
                import builtins
                builtins.print = builtins.__original_print__ if hasattr(builtins, "__original_print__") else print
                print("Debug-läge aktiverat")
                continue
            
            if question.lower() == "debug off":
                debug_mode = False
                disable_client_prints()
                print("Debug-läge inaktiverat")
                continue
            
            if not question.strip():
                continue
            
            print("Bearbetar frågan...")
            response = ask_question(question)
            
            print("\nSvar:")
            if "error" in response:
                print(f"Fel: {response['error']}")
            else:
                print(f"Server: {response['server']}")
                print(f"Operation: {response['operation']}")
                print(f"Resultat: {response['result']}")
                if "note" in response:
                    print(f"Notering: {response['note']}")
        except KeyboardInterrupt:
            print("\nAvslutar...")
            break
        except Exception as e:
            print(f"Ett fel uppstod: {e}")

def run_test_questions():
    """Kör några testfrågor"""
    print("=== Testfrågor ===")
    
    test_questions = [
        "Vad är 5 + 3?",
        "Vänd texten 'Hej världen'!",
        "Vad är kvadraten av 7?",
        "Hur många tecken finns i 'programmering'?"
    ]
    
    for question in test_questions:
        print(f"\nFråga: {question}")
        response = ask_question(question)
        if "error" in response:
            print(f"Fel: {response['error']}")
        else:
            print(f"Server: {response['server']}")
            print(f"Operation: {response['operation']}")
            print(f"Resultat: {response['result']}")
        # Lägg till en kort paus mellan frågorna
        time.sleep(0.5)

if __name__ == "__main__":
    # Spara original print-funktionen
    import builtins
    builtins.__original_print__ = builtins.print
    
    # Visa information om tillgängliga servrar
    print_server_info()
    
    # Kör testfrågor
    run_test_questions()
    
    # Starta interaktivt läge
    interactive_mode()