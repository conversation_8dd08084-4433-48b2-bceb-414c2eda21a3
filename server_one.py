# === FastMCP Server ===
# server_one.py

from fastmcp import FastMCP
import uvicorn
from fastapi import <PERSON><PERSON><PERSON>, Request, Response
from fastapi.middleware.cors import CORSMiddleware
import json

# Create a FastMCP server
mcp = FastMCP("SimpleCalculator")

# Add an addition tool
@mcp.tool()
def add(a: int, b: int) -> int:
    """Add two numbers"""
    print(f"ADDITION received: {a} + {b}")
    return a + b

# Add a multiplication tool
@mcp.tool()
def multiply(a: int, b: int) -> int:
    """Multiply two numbers"""
    print(f"MULTIPLICATION received: {a} * {b}")
    return a * b

# Add a multi-multiplication tool for handling multiple numbers
@mcp.tool()
def multiply_many(numbers: list) -> int:
    """Multiply multiple numbers"""
    print(f"MULTI-MULTIPLICATION received: {numbers}")
    if not numbers:
        return 0
    result = 1
    for num in numbers:
        result *= num
    return result

# Add a greeting resource
@mcp.resource("greeting://{name}")
def get_greeting(name: str) -> str:
    """Get a personalized greeting"""
    return f"Hello, {name}!"

# Add a simple prompt template
@mcp.prompt("calculator_help")
def calculator_help() -> str:
    """Return help text for the calculator"""
    return """
    This is a simple calculator that can add and multiply numbers.
    
    Available tools:
    - add(a, b): Add two numbers
    - multiply(a, b): Multiply two numbers
    - multiply_many(numbers): Multiply multiple numbers
    
    Example usage:
    - add(5, 3) = 8
    - multiply(4, 7) = 28
    - multiply_many([1, 2, 3, 4]) = 24
    """

# Create a FastAPI app
app = FastAPI()

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Add a simple endpoint to check if the server is running
@app.get("/")
async def root():
    return {"message": "FastMCP Calculator Server is running"}

# Add an endpoint to handle requests in the format similar to your existing servers
@app.post("/ask")
async def ask(request: Request):
    data = await request.json()
    operation = data.get("operation", "add")
    a = data.get("a", 0)
    b = data.get("b", 0)
    numbers = data.get("numbers", [])
    
    print(f"Received request with operation: {operation}")
    print(f"Data: {data}")
    
    if operation == "add":
        result = add(a, b)
        return {"result": result, "operation": "addition"}
    elif operation == "multiply":
        result = multiply(a, b)
        return {"result": result, "operation": "multiplication"}
    elif operation == "multiply_many":
        print(f"Multiplying numbers: {numbers}")
        result = multiply_many(numbers)
        print(f"Result: {result}")
        return {"result": result, "operation": "multi-multiplication"}
    else:
        return {"error": f"Unknown operation: {operation}"}

# Run the server
if __name__ == "__main__":
    print("Starting FastMCP Calculator Server on http://127.0.0.1:8004")
    uvicorn.run(app, host="127.0.0.1", port=8004)
