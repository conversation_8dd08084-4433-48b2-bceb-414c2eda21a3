# === MCP System Launcher ===
# run.py

import os
import sys
import time
import subprocess
import signal
import atexit

# List of processes to be started
processes = []

# Function to terminate all processes on exit
def cleanup():
    print("\nTerminating all processes...")
    for process in processes:
        try:
            if process.poll() is None:  # If process is still running
                process.terminate()
                # Wait a short while for the process to terminate
                time.sleep(0.5)
                # If process is still running, use kill
                if process.poll() is None:
                    process.kill()
        except Exception as e:
            print(f"Error terminating process: {e}")

# Register cleanup function to run on exit
atexit.register(cleanup)

# Handle Ctrl+C
def signal_handler(sig, frame):
    print("\nCtrl+C detected. Exiting...")
    sys.exit(0)

signal.signal(signal.SIGINT, signal_handler)

def start_server(server_file, server_name):
    """Start a server and add it to the process list"""
    print(f"Starting {server_name}...")
    # Start the process in a new window with its own title
    if os.name == 'nt':  # Windows
        process = subprocess.Popen(
            ["start", "cmd", "/k", f"title {server_name} && python {server_file}"],
            shell=True,
            creationflags=subprocess.CREATE_NEW_CONSOLE
        )
    else:  # Linux/Mac
        process = subprocess.Popen(
            ["python", server_file],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )

    processes.append(process)
    # Wait a short while to let the server start
    time.sleep(1)
    return process

def start_host():
    """Start host.py in current console"""
    print("Starting host.py...")
    if os.name == 'nt':  # Windows
        # For Windows, start host.py in current console
        os.system("python host.py")
    else:  # Linux/Mac
        # For Linux/Mac, start host.py as a subprocess
        host_process = subprocess.Popen(["python", "host.py"])
        processes.append(host_process)
        host_process.wait()

def main():
    """Main function to start all components"""
    print("=== MCP System Launcher ===")
    print("Starting all servers...")

    # Start all servers in separate windows
    start_server("server_one.py", "Math Server")
    start_server("server_two.py", "Text Server")
    start_server("server_three.py", "Advanced Math Server")

    # Wait a bit to ensure all servers have started
    print("Waiting for all servers to start...")
    time.sleep(2)

    # Start host.py in current console
    start_host()

if __name__ == "__main__":
    main()
