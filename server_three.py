# === FastMCP Advanced Math Server ===
# server_three.py

from fastmcp import FastMCP
import uvicorn
from fastapi import FastAP<PERSON>, Request, Response
from fastapi.middleware.cors import CORSMiddleware
import json
import math

# Create a FastMCP server
mcp = FastMCP("AdvancedMathProcessor")

# Add a square tool
@mcp.tool()
def square(x: float) -> float:
    """Square a number"""
    print(f"SQUARE received: {x}")
    return x * x

# Add a cube tool
@mcp.tool()
def cube(x: float) -> float:
    """Cube a number"""
    print(f"CUBE received: {x}")
    return x * x * x

# Add a square root tool
@mcp.tool()
def sqrt(x: float) -> float:
    """Calculate the square root of a number"""
    print(f"SQRT received: {x}")
    if x < 0:
        return {"error": "Cannot calculate square root of a negative number"}
    return math.sqrt(x)

# Add a math constant resource
@mcp.resource("constant://{name}")
def get_constant(name: str) -> float:
    """Get a mathematical constant by name"""
    constants = {
        "pi": math.pi,
        "e": math.e,
        "golden_ratio": 1.618033988749895,
        "sqrt2": math.sqrt(2)
    }
    return constants.get(name, 0)

# Add a simple prompt template
@mcp.prompt("advanced_math_help")
def advanced_math_help() -> str:
    """Return help text for the advanced math processor"""
    return """
    This is an advanced math processor that can perform various operations on numbers.
    
    Available tools:
    - square(x): Square a number
    - cube(x): Cube a number
    - sqrt(x): Calculate the square root of a number
    
    Example usage:
    - square(5) = 25
    - cube(3) = 27
    - sqrt(16) = 4
    """

# Create a FastAPI app
app = FastAPI()

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Add a simple endpoint to check if the server is running
@app.get("/")
async def root():
    return {"message": "FastMCP Advanced Math Processor Server is running"}

# Add an endpoint to handle requests in the format similar to your existing servers
@app.post("/ask")
async def ask(request: Request):
    data = await request.json()
    operation = data.get("operation", "square")
    x = data.get("x", 0)
    
    if operation == "square":
        result = square(x)
        return {"result": result, "operation": "square"}
    elif operation == "cube":
        result = cube(x)
        return {"result": result, "operation": "cube"}
    elif operation == "sqrt":
        result = sqrt(x)
        return {"result": result, "operation": "sqrt"}
    else:
        return {"error": f"Unknown operation: {operation}"}

# Run the server
if __name__ == "__main__":
    print("Starting FastMCP Advanced Math Processor Server on http://127.0.0.1:8006")
    uvicorn.run(app, host="127.0.0.1", port=8006)
